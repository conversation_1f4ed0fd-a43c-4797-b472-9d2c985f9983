<script lang="ts">
	import type { SolitarySession } from '$lib/types/session.types';

	interface SolitaryCardProps extends SolitarySession {}
	const { title, author, createdAt, owner, guest, sessionId }: SolitaryCardProps = $props();
</script>

<a
	class="group relative h-[540px] w-96 max-w-96 min-w-96 cursor-pointer transition-all duration-500 ease-out hover:-translate-y-2 hover:scale-[1.02]"
	href={`/session/${sessionId}`}
>
	<div
		class="relative h-full overflow-hidden rounded-3xl border border-zinc-800 from-zinc-900 to-zinc-950 transition-all duration-500 ease-out group-hover:border-gray-500/50 group-hover:shadow-2xl group-hover:shadow-gray-500/20"
	>
		<div
			class="absolute top-0 right-0 h-24 w-24 bg-zinc-950"
			style="clip-path: polygon(30% 0%, 100% 0%, 100% 70%)"
		></div>
		<div
			class="absolute top-0 right-0 h-24 w-24 bg-gradient-to-br from-gray-600/20 to-transparent"
			style="clip-path: polygon(32% 0%, 100% 0%, 100% 68%)"
		></div>

		<header class="relative z-10 flex items-start justify-between p-8">
			<div>
				<p class="mb-1 text-xs font-medium tracking-widest text-zinc-500 uppercase">Created</p>
				<time class="text-sm text-zinc-300"> {createdAt} </time>
			</div>

			<div
				class="mr-8 flex items-center gap-2 rounded-full border border-zinc-700/50 bg-zinc-800/50 px-4 py-2 backdrop-blur-sm"
			>
				<div class="h-1.5 w-1.5 animate-pulse rounded-full bg-gray-400"></div>
				<span class="text-sm font-medium text-zinc-300">{guest} / {owner}</span>
			</div>
		</header>

		<main class="flex h-[320px] items-center justify-center px-10">
			<blockquote class="text-center">
				<p
					class="playwrite-hu line-clamp-4 text-3xl leading-relaxed font-light tracking-tight text-zinc-100"
				>
					"{title}"
				</p>
				<footer class="mt-6 truncate text-sm text-zinc-500">— {author}</footer>
			</blockquote>
		</main>

		<!-- Animated Footer -->
		<footer class="absolute right-0 bottom-0 left-0 h-28 border-t border-zinc-800/50">
			<!-- Subtle grid pattern -->
			<div
				class="absolute inset-0 opacity-[0.02]"
				style="background-image: linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px); background-size: 20px 20px;"
			></div>

			<!-- Animated elements -->
			<div class="relative h-full">
				<!-- DNA-like helix animation -->
				<svg class="absolute inset-0 h-full w-full" viewBox="0 0 400 112">
					<g class="opacity-20">
						{#each Array(8) as _, i}
							<circle
								cx={50 + i * 50}
								cy="56"
								r="1.5"
								fill="white"
								class="animate-pulse"
								style="animation-delay: {i * 0.2}s; animation-duration: 3s;"
							>
								<animate
									attributeName="cy"
									values="56;40;56;72;56"
									dur="4s"
									begin="{i * 0.3}s"
									repeatCount="indefinite"
								/>
								<animate
									attributeName="opacity"
									values="0.2;0.8;0.2"
									dur="4s"
									begin="{i * 0.3}s"
									repeatCount="indefinite"
								/>
							</circle>
						{/each}
					</g>

					<!-- Connecting lines -->
					<path
						d="M 50 56 Q 100 40, 150 56 T 250 56 T 350 56"
						stroke="url(#gradient)"
						stroke-width="0.5"
						fill="none"
						class="opacity-30"
					>
						<animate
							attributeName="d"
							values="M 50 56 Q 100 40, 150 56 T 250 56 T 350 56;
                        M 50 56 Q 100 72, 150 56 T 250 56 T 350 56;
                        M 50 56 Q 100 40, 150 56 T 250 56 T 350 56"
							dur="4s"
							repeatCount="indefinite"
						/>
					</path>

					<defs>
						<linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
							<stop offset="0%" stop-color="#a78bfa" />
							<stop offset="50%" stop-color="#c084fc" />
							<stop offset="100%" stop-color="#a78bfa" />
						</linearGradient>
					</defs>
				</svg>

				<!-- Energy bar -->
				<div
					class="absolute right-0 bottom-0 left-0 h-px bg-gradient-to-r from-transparent via-gray-500/50 to-transparent"
				></div>
			</div>
		</footer>
	</div>
</a>
