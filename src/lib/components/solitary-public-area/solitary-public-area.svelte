<script>
	import SolitaryCard from '$lib/components/solitary-card/solitary-card.svelte';
	import { Input } from '$lib/components/ui/input';
	import { globalState } from '$lib/states/global.state.svelte';

	$effect(() => {
		globalState.loadSessions();
	});
</script>

<section class="px-5">
	<div class="mb-8 flex justify-center">
		<div class="w-full max-w-md">
			<Input
				type="text"
				placeholder="Search sessions by title or author..."
				bind:value={globalState.searchQuery}
				class="h-16 rounded-3xl border-zinc-700 bg-black px-6 text-zinc-100 placeholder:text-zinc-500 focus:border-zinc-700 focus:ring-zinc-700/20"
			/>
		</div>
	</div>

	<div class="flex flex-wrap justify-center gap-10">
		{#each globalState.filteredSessions() as session (session.sessionId)}
			<SolitaryCard {...session} />
		{/each}
	</div>

	{#if globalState.filteredSessions().length === 0}
		<p class="mt-10 text-center text-zinc-500">No sessions found.</p>
	{/if}
</section>
