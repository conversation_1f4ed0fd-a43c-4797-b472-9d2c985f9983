<script>
	import SolitaryCard from '$lib/components/solitary-card/solitary-card.svelte';
	import { globalState } from '$lib/states/global.state.svelte';

	$effect(() => {
		globalState.loadSessions();
	});
</script>

<section class="px-5">
	<!-- search input centered here -->
	seaarch input centered
	<!-- search input centered here -->
	<div class="flex flex-wrap justify-center gap-10">
		{#each globalState.currentSessions as session (session.sessionId)}
			<SolitaryCard {...session} />
		{/each}
	</div>
</section>
