<script lang="ts">
	import { fade } from 'svelte/transition';
	import Icon from '../ui/icon.svelte';
	import SolitaryAnimated from '../ui/solitary-animated.svelte';

	let visible = $state(false);

	$effect(() => {
		visible = true;
	});
</script>

<div
	class="fixed top-0 z-10 flex h-[60px] w-full items-center justify-between bg-black/70 pr-4 backdrop-blur-sm"
>
	{#if visible}
		<header
			in:fade={{ delay: 100, duration: 2000 }}
			class="flex w-full items-center justify-between"
		>
			<SolitaryAnimated />
			<a class="playwrite-hu text-xl font-semibold" href="/">Solitary</a>
			<Icon icon="bi:info" class="cursor-pointer rounded-full p-1 hover:bg-gray-900/50" />
		</header>
	{/if}
</div>
