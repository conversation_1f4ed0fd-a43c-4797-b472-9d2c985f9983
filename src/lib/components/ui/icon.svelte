<script lang="ts">
	import { cn } from '$lib/utils';

	interface IconProps {
		icon?: string;
		class?: string;
		onclick?: () => void;
	}

	const { icon = 'md:home', class: className }: IconProps = $props();
</script>

<!-- svelte-ignore attribute_global_event_reference -->
<!-- svelte-ignore a11y_click_events_have_key_events -->
<!-- svelte-ignore a11y_no_static_element_interactions -->
<iconify-icon {icon} class={cn('inline-block text-white', className)} {onclick}></iconify-icon>
