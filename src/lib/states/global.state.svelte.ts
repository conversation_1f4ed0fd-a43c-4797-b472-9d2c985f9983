import type { SolitarySession } from "$lib/types/session.types";

class GlobalState {
    currentSessions: SolitarySession[] = $state([]);

    loadSessions() {
        this.currentSessions = [
            {
                title: "Morning Philosophy Discussion",
                author: "alice_thinker",
                createdAt: "2024-01-15T09:30:00Z",
                owner: 1,
                guest: 0,
                sessionId: "sess_001"
            },
            {
                title: "Code Review Session",
                author: "dev_master",
                createdAt: "2024-01-15T14:15:00Z",
                owner: 1,
                guest: 1,
                sessionId: "sess_002"
            },
            {
                title: "Book Club: The Stranger",
                author: "literature_lover",
                createdAt: "2024-01-15T19:00:00Z",
                owner: 1,
                guest: 0,
                sessionId: "sess_003"
            }
        ];
    }
}

export const globalState = new GlobalState();